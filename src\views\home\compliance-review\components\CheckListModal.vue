<template>
  <a-modal
    v-model:open="visible"
    title="审查清单"
    width="800px"
    :footer="null"
    @cancel="handleClose"
  >
    <div class="checklist-modal">
      <a-spin :spinning="loading">
        <div class="checklist-header">
          <div class="checklist-info">
            <h4>{{ checkList?.name || '审查清单' }}</h4>
            <p class="version">版本: {{ checkList?.version || 'v1.0' }}</p>
          </div>
          <div class="progress-info">
            <a-progress 
              :percent="completionRate" 
              :stroke-color="progressColor"
              size="small"
            />
            <span class="progress-text">
              完成进度: {{ completedCount }}/{{ totalCount }}
            </span>
          </div>
        </div>

        <div class="checklist-content">
          <a-collapse v-model:activeKey="activeKeys" ghost>
            <a-collapse-panel 
              v-for="category in categories" 
              :key="category"
              :header="category"
            >
              <template #extra>
                <a-tag :color="getCategoryColor(category)">
                  {{ getCategoryStats(category) }}
                </a-tag>
              </template>
              
              <div class="category-items">
                <div 
                  v-for="item in getCategoryItems(category)" 
                  :key="item.id"
                  :class="['checklist-item', { 'completed': item.completed }]"
                >
                  <div class="item-header">
                    <a-checkbox 
                      v-model:checked="item.completed"
                      :disabled="!item.required"
                      @change="handleItemToggle(item)"
                    >
                      <span class="item-title">{{ item.title }}</span>
                      <a-tag v-if="item.required" color="red" size="small">必填</a-tag>
                    </a-checkbox>
                    
                    <div class="item-status">
                      <a-tag 
                        v-if="item.riskLevel" 
                        :color="getRiskColor(item.riskLevel)"
                        size="small"
                      >
                        {{ getRiskLabel(item.riskLevel) }}
                      </a-tag>
                    </div>
                  </div>
                  
                  <div class="item-description">
                    {{ item.description }}
                  </div>
                  
                  <div v-if="item.notes" class="item-notes">
                    <a-textarea 
                      v-model:value="item.notes"
                      placeholder="备注信息..."
                      :rows="2"
                      @blur="handleNotesChange(item)"
                    />
                  </div>
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>

        <div class="checklist-footer">
          <div class="footer-stats">
            <a-statistic 
              title="总项目" 
              :value="totalCount" 
              :value-style="{ color: '#1890ff' }"
            />
            <a-statistic 
              title="已完成" 
              :value="completedCount" 
              :value-style="{ color: '#52c41a' }"
            />
            <a-statistic 
              title="必填项" 
              :value="requiredCount" 
              :value-style="{ color: '#ff4d4f' }"
            />
          </div>
          
          <div class="footer-actions">
            <a-button @click="handleClose">关闭</a-button>
            <a-button type="primary" @click="handleSave" :loading="saving">
              保存
            </a-button>
          </div>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import type { CheckList, CheckListItem, RiskLevel } from '@/types/compliance'
import { apiGetCheckList } from '@/api/compliance'
import { mockData } from '../mock-data'

defineOptions({
  name: 'CheckListModal'
})

interface Props {
  open: boolean
  taskId: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:open': [value: boolean]
  'save': [checkList: CheckList]
}>()

// 响应式状态
const visible = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})

const loading = ref(false)
const saving = ref(false)
const checkList = ref<CheckList | null>(null)
const activeKeys = ref<string[]>([])

// 计算属性
const categories = computed(() => checkList.value?.categories || [])

const totalCount = computed(() => checkList.value?.items.length || 0)

const completedCount = computed(() => 
  checkList.value?.items.filter(item => item.completed).length || 0
)

const requiredCount = computed(() => 
  checkList.value?.items.filter(item => item.required).length || 0
)

const completionRate = computed(() => {
  if (totalCount.value === 0) return 0
  return Math.round((completedCount.value / totalCount.value) * 100)
})

const progressColor = computed(() => {
  const rate = completionRate.value
  if (rate < 30) return '#ff4d4f'
  if (rate < 70) return '#faad14'
  return '#52c41a'
})

// 方法
const getCategoryItems = (category: string) => {
  return checkList.value?.items.filter(item => item.category === category) || []
}

const getCategoryStats = (category: string) => {
  const items = getCategoryItems(category)
  const completed = items.filter(item => item.completed).length
  return `${completed}/${items.length}`
}

const getCategoryColor = (category: string) => {
  const items = getCategoryItems(category)
  const completed = items.filter(item => item.completed).length
  const rate = items.length > 0 ? completed / items.length : 0
  
  if (rate === 1) return 'green'
  if (rate > 0.5) return 'orange'
  return 'red'
}

const getRiskColor = (riskLevel: RiskLevel) => {
  const colors = {
    risk: '#ff4d4f',
    safe: '#52c41a',
    na: '#d9d9d9'
  }
  return colors[riskLevel] || '#d9d9d9'
}

const getRiskLabel = (riskLevel: RiskLevel) => {
  const labels = {
    risk: '发现风险',
    safe: '未发现风险',
    na: '不适用'
  }
  return labels[riskLevel] || '未知'
}

const handleItemToggle = (item: CheckListItem) => {
  // 项目状态切换逻辑
  console.log('切换项目状态:', item)
}

const handleNotesChange = (item: CheckListItem) => {
  // 备注变更逻辑
  console.log('备注变更:', item)
}

const handleClose = () => {
  visible.value = false
}

const handleSave = async () => {
  if (!checkList.value) return
  
  try {
    saving.value = true
    // 这里应该调用保存API
    emit('save', checkList.value)
    message.success('保存成功')
    handleClose()
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

const fetchCheckList = async () => {
  if (!props.taskId) return

  try {
    loading.value = true

    // 开发环境使用模拟数据
    if (import.meta.env.VITE_ENV === 'dev') {
      await new Promise(resolve => setTimeout(resolve, 500))
      checkList.value = mockData.checkList
      activeKeys.value = mockData.checkList.categories
      return
    }

    const { data, err } = await apiGetCheckList(props.taskId)
    if (err) {
      message.error('获取审查清单失败')
      return
    }

    checkList.value = data
    // 默认展开所有分类
    activeKeys.value = data.categories
  } catch (error) {
    console.error('获取审查清单失败:', error)
    message.error('获取审查清单失败')
  } finally {
    loading.value = false
  }
}

// 监听弹窗打开状态
watch(() => props.open, (newVal) => {
  if (newVal) {
    fetchCheckList()
  }
})
</script>

<style lang="scss" scoped>
.checklist-modal {
  .checklist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--line-2);
    margin-bottom: 16px;
    
    .checklist-info {
      h4 {
        margin: 0 0 4px 0;
        font-size: var(--font-16);
        color: var(--text-5);
      }
      
      .version {
        margin: 0;
        color: var(--text-3);
        font-size: var(--font-12);
      }
    }
    
    .progress-info {
      text-align: right;
      
      .progress-text {
        display: block;
        margin-top: 4px;
        font-size: var(--font-12);
        color: var(--text-3);
      }
    }
  }
  
  .checklist-content {
    max-height: 500px;
    overflow-y: auto;
    
    .category-items {
      .checklist-item {
        padding: 12px;
        margin-bottom: 8px;
        border: 1px solid var(--line-2);
        border-radius: 6px;
        transition: all 0.2s;
        
        &:hover {
          border-color: var(--main-6);
        }
        
        &.completed {
          background: var(--success-1);
          border-color: var(--success-6);
        }
        
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          .item-title {
            font-weight: 500;
            color: var(--text-5);
          }
        }
        
        .item-description {
          color: var(--text-3);
          font-size: var(--font-14);
          line-height: 1.5;
          margin-bottom: 8px;
          padding-left: 24px;
        }
        
        .item-notes {
          padding-left: 24px;
        }
      }
    }
  }
  
  .checklist-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-top: 1px solid var(--line-2);
    margin-top: 16px;
    
    .footer-stats {
      display: flex;
      gap: 24px;
    }
    
    .footer-actions {
      display: flex;
      gap: 8px;
    }
  }
}

:deep(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header) {
  padding: 12px 0;
  font-weight: 500;
}

:deep(.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box) {
  padding: 0 0 16px 0;
}
</style>
