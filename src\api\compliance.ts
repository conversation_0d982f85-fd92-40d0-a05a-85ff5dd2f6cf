import { http } from '@/services/http'
import type { 
  ReviewTask, 
  ReviewItem, 
  CheckList, 
  ExportConfig,
  ApiResponse 
} from '@/types/compliance'

/**
 * 获取审查任务详情
 */
export const apiGetReviewTask = (taskId: string) => 
  http<ApiResponse<ReviewTask>>({
    url: `/compliance/v1/review/task/${taskId}`,
    method: 'get'
  })

/**
 * 获取审查项目列表
 */
export const apiGetReviewItems = (taskId: string, params?: {
  riskLevel?: string
  page?: number
  pageSize?: number
}) => 
  http<ApiResponse<{
    items: ReviewItem[]
    total: number
    page: number
    pageSize: number
  }>>({
    url: `/compliance/v1/review/items/${taskId}`,
    method: 'get',
    params
  })

/**
 * 更新审查项目
 */
export const apiUpdateReviewItem = (itemId: string, data: Partial<ReviewItem>) =>
  http<ApiResponse<ReviewItem>>({
    url: `/compliance/v1/review/item/${itemId}`,
    method: 'put',
    data
  })

/**
 * 获取审查清单
 */
export const apiGetCheckList = (taskId: string) =>
  http<ApiResponse<CheckList>>({
    url: `/compliance/v1/review/checklist/${taskId}`,
    method: 'get'
  })

/**
 * 导出审查报告
 */
export const apiExportReviewReport = (taskId: string, config: ExportConfig) =>
  http<Blob>({
    url: `/compliance/v1/review/export/${taskId}`,
    method: 'post',
    data: config,
    responseType: 'blob'
  })

/**
 * 获取PDF文件信息
 */
export const apiGetPdfInfo = (fileId: string) =>
  http<ApiResponse<{
    fileUrl: string
    fileName: string
    pageCount: number
    fileSize: number
  }>>({
    url: `/file/v1/pdf/info/${fileId}`,
    method: 'get'
  })

/**
 * 保存PDF标注
 */
export const apiSavePdfAnnotation = (data: {
  taskId: string
  itemId: string
  pageNumber: number
  coordinates: {
    x: number
    y: number
    width: number
    height: number
  }
  annotationType: 'highlight' | 'note' | 'risk'
  content?: string
}) =>
  http<ApiResponse<any>>({
    url: '/compliance/v1/review/annotation',
    method: 'post',
    data
  })

/**
 * 获取PDF标注列表
 */
export const apiGetPdfAnnotations = (taskId: string) =>
  http<ApiResponse<Array<{
    id: string
    itemId: string
    pageNumber: number
    coordinates: {
      x: number
      y: number
      width: number
      height: number
    }
    annotationType: string
    content?: string
    createdAt: string
  }>>>({
    url: `/compliance/v1/review/annotations/${taskId}`,
    method: 'get'
  })

/**
 * 删除审查项目
 */
export const apiDeleteReviewItem = (itemId: string) =>
  http<ApiResponse<any>>({
    url: `/compliance/v1/review/item/${itemId}`,
    method: 'delete'
  })

/**
 * 批量更新审查项目状态
 */
export const apiBatchUpdateItems = (data: {
  taskId: string
  itemIds: string[]
  riskLevel: string
  notes?: string
}) =>
  http<ApiResponse<any>>({
    url: '/compliance/v1/review/items/batch',
    method: 'put',
    data
  })

/**
 * 获取审查统计信息
 */
export const apiGetReviewStats = (taskId: string) =>
  http<ApiResponse<{
    totalItems: number
    riskItems: number
    safeItems: number
    naItems: number
    completedItems: number
    progress: number
  }>>({
    url: `/compliance/v1/review/stats/${taskId}`,
    method: 'get'
  })

/**
 * 提交审查结果
 */
export const apiSubmitReview = (taskId: string, data: {
  summary: string
  conclusion: string
  recommendations?: string[]
}) =>
  http<ApiResponse<any>>({
    url: `/compliance/v1/review/submit/${taskId}`,
    method: 'post',
    data
  })
