<template>
  <div class="home">
    <!-- 主容器卡片 -->
    <div class="main-card">
      <!-- 标题区域 -->
      <div class="header-section"> 
          <img class="welcome-icon" src="@/assets/images/bid-examine/home-welcome.png" alt="欢迎图标">
          <span class="title-text">您好，欢迎使用采购文件合规性审查</span> 
      </div>

      <!-- 上传区域 -->
      <div class="upload-section">
        <upload-file
          ref="uploadFileRef"
          v-model:files="fileList"
          accept=".docx"
          accept-tip="文件格式不正确，请选择.docx文件"
          :show-upload-list="false"
          @change="doChange">
          <div class="upload-area">
            <img class="upload-icon" src="@/assets/images/bid-examine/upload-circle.png" alt="上传"> 
            <!-- 上传文字 -->
            <div class="upload-text-main">上传采购文件</div>
            <div class="upload-text-sub">仅支持 .docx 格式文档，单个文档大小不超过 20MB</div> 
            <div class="upload-text-hint">或将文件拖拽到此处</div>
          </div>
        </upload-file>
      </div>

      <!-- 文件列表区域 -->
      <div v-show="fileList.length > 0" class="file-list-section">
        <!-- 文件列表标题和按钮 -->
        <div class="file-list-header">
          <div class="file-count-text">已上传文件 ({{ fileList.filter(item=>item.status === 'done').length }}/{{ Math.max(0, fileList.length) }})</div>
          <div class="header-actions"> 
            <a-button type="primary" class="start-button" :class="{ disabled: !doneCount }" @click="handleStartReview">
              开始审查
            </a-button>
          </div>
        </div>

        <!-- 文件项列表 -->
        <div class="file-items-container">
          <div v-for="file in fileList" :key="file.uid" class="file-item" :class="`file-item-${file.status}`">
            <div class="file-close-btn" @click="handleRemoveFile(file)">
               <CloseOutlined :style="{'font-size': '8px'}"/>
            </div>
            <div class="file-detail">
              <svg-icon :icon="getFileIcon(file.name)" class="icon-file" /> 
              <div class="file-info">
                <div class="file-name" :title="file.name">{{ file.name }}</div>
                <div class="file-size">{{ file.size }}</div>
              </div>
            </div>
            <div class="file-status">
              <div :class="`status-${file.status}`">
                <component class="status-icon" :is="getStatusIcon(file.status).icon" />
                <span class="status-text">{{ getStatusIcon(file.status).text }}</span>
              </div>
              <a-button v-if="file.status === 'error'" class="retry-btn" type="link" @click="handleRetryUpload(file)">重试</a-button>
            </div>

            <!-- 进度条 -->
            <div  class="progress-bar">
              <div class="progress-fill" :class="file.status" :style="{ width: `${file.percent || 0}%` }"></div>
            </div> 
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { computed, ref } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import {apiTaskCreate} from '@/api/examine'
import { useRouter } from 'vue-router'
import { getFileIcon, getStatusIcon } from '@/views/home/<USER>/examine'
import UploadFile from '@/components/UploadFile/index.vue'
import { message } from 'ant-design-vue'

interface FileItem {
  uid: string
  name: string
  size: number
  status: 'uploading' | 'done' | 'error'
  percent?: number
  response?: any
}
const router = useRouter()
const fileList = ref<FileItem[]>([])
const uploadFileRef = ref()

function doChange(files:any) { 
  fileList.value = [...files] 
}

// 计算是否可以开始审查
const doneCount = computed(() => {
  return fileList.value.filter(file => file.status === 'done').length
}) 
// 删除文件
function handleRemoveFile(file: FileItem) {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index !== -1) {
    fileList.value.splice(index, 1)
  }
}

// 重试上传
async function handleRetryUpload(file: FileItem) {
  await uploadFileRef.value?.handleRetry(file) 
}
const isStarting = ref(false)
async function handleStartReview() {
  if (!doneCount.value || isStarting.value) return 
  const fileIdList = fileList.value.map((item: any) => item.response?.fileId)
  isStarting.value = true
  const {err,data} = await apiTaskCreate({fileIdList})
  isStarting.value = false
  if (err) return
  if(!data.taskIdList.length) {
    message.error('审查失败') 
    return
  }
  router.push({
    name: 'ResultIndex',
    query: { taskId:data.taskIdList[0] }
  })
} 
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
  background-color: #F9FAFB;
  color: #000;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  box-sizing: border-box; 
  .main-card {     
    width: 1280px;
    background: #FFFFFF;
    border-radius: 16px;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    padding: 32px; 
  }

  .header-section {
    display: flex; 
    align-items: center; 
    justify-content: center;
    height: 48px;
    .welcome-icon {
      width: 48px;
      height: 48px;
      flex-shrink: 0;
      margin-right: 16px;
    }
    .title-text { 
      font-weight: 600;
      font-size: 30px; 
      color: #111827; 
    }
  }
:deep(.ant-upload-wrapper) {
    .ant-upload-select {
      width: 100%;
    }
  }
  .upload-section {
    width: 100%;
    margin-top: 32px;
  }

  .upload-area {
    width: 100%;
    height: 246px;
    background: #F7F8FA;
    border: 1px solid #E5E6EB;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;     
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-10px);
    } 
    .upload-icon {
      width: 64px;
      height: 64px;
      display: block;
    }

    .upload-text-main { 
      font-weight: 400;
      font-size: 20px;
      line-height: 1.4em;
      color: #111827;
      text-align: center;
      margin-top: 16px;
    }
    .upload-text-hint,
    .upload-text-sub{
      font-weight: 400; 
      margin-top: 12px;
    } 
    .upload-text-sub {  
      font-size: 16px; 
      color: #4B5563;
      text-align: center; 
    }

    .upload-text-hint { 
      color: #6B7280;
      font-size: 14px;
      line-height: 20px;
    }
  }

  // 文件列表区域
  .file-list-section {
    display: flex;
    flex-direction: column;  
    .file-list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin:32px 0 24px;
      .file-count-text { 
        font-weight: 600;
        font-size: 20px;
        line-height: 1.4em;
        color: #000000; 
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 16px;

        .test-button {
          padding: 8px 16px;
          height: 32px;
          background: #F0F5FF;
          border: 1px solid #8FB0FF;
          border-radius: 4px;
          font-family: 'Inter', sans-serif;
          font-weight: 400;
          font-size: 14px;
          color: #133CE8;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #E6EFFF;
            border-color: #3B66F5;
          }
        }

        .start-button {
          width: 138px;
          height: 48px;
          background: #133CE8;
          border-radius: 6px;
          border: none;
          font-family: 'Inter', sans-serif;
          font-weight: 600;
          font-size: 16px;
          line-height: 1.5em;
          color: #FFFFFF;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #3B66F5;
          }

          &:active {
            background: #0625C2;
          }

          &.disabled {
            background: #D1D5DB;
            color: #6B7280;
            cursor: not-allowed;
          }
        }
      }
    }

    .file-items-container {
      display: flex;
      flex-direction: row;
      gap: 16px;
      flex-wrap: wrap;
    }

    .file-item {
      position: relative;
      width: 394px;
      padding: 16px;
      border-radius: 8px;
      background: #FFFFFF;
      border: 1px solid #E5E6EB; 
      gap: 16px;  
      &.file-item-done {
        background: #F6FFED;
        border-color: #B7EB8F;
      }

      &.file-item-error {
        background: #FFF1F0;
        border-color: #FFA39E;
      }

      &.file-item-uploading {
        background: #F0F5FF;
        border-color: #8FB0FF;
      }

      .file-close-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        background: #FFFFFF;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
                    0px 3px 6px -4px rgba(0, 0, 0, 0.12),
                    0px 6px 16px 0px rgba(0, 0, 0, 0.08);
        color: #000000; 
      } 
      .file-detail {
        display: flex;
        align-items: center;
        width: 100%;
      }
      .icon-file {
        width: 32px;
        height: 32px;
        flex-shrink: 0;
        margin-right: 4px;
      }
      .file-info {
        flex: 1;
        min-width: 0; 

        .file-name {  
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis; 
          height: 22px;
          line-height: 22px;
          color: rgba(0, 0, 0, 0.88);
        } 
        .file-size { 
          color: rgba(0, 0, 0, 0.45);
        }
      }

      .file-status { 
        margin: 12px 0;
        display: flex;
        align-items: center;  
        justify-content: space-between;
        .status-done,
        .status-error,
        .status-uploading {
          display: flex;
          align-items: center;
          gap: 4px; 
        }
        .status-icon {
          width: 16px;
          height: 16px;
        }

        .status-done {
          color:var(--success-6);
        }

        .status-uploading {
          color: var(--main-6); 
        }

        .status-error {
          color: var(--error-6); 
        }
        .retry-btn {
          padding: 0;
          margin: 0;
          height: 22px;
          &:hover {
            color: #3B66F5;
          }
        }
      }

      .progress-bar { 
        height: 6px;
        background: #FFFFFF;
        border-radius: 6px;
        overflow: hidden; 
        .progress-fill {
          height: 100%;
          border-radius: 6px;  
          background: var(--main-6); 

          &.done {
            background: var(--success-6);
          }

          &.error {
            background: var(--error-6);
          }
        }
      }
    }
  }
}
</style>
