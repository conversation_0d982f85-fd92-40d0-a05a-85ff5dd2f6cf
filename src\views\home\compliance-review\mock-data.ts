import type { ReviewTask, ReviewItem, CheckList, CheckListItem } from '@/types/compliance'

// 模拟审查任务数据
export const mockReviewTask: ReviewTask = {
  id: '1',
  taskName: '深圳市宝安区人民医院皮肤科设备采购审查',
  fileName: '深圳市宝安区人民医院皮肤科设备一批采购.pdf',
  fileUrl: '/static/sample.pdf',
  reviewTime: '2024-01-15 09:30:00',
  status: 'in_progress',
  totalItems: 16,
  riskItems: 6,
  safeItems: 5,
  naItems: 5,
  items: []
}

// 模拟审查项目数据
export const mockReviewItems: any[] = [
  {
    id: '1',
    reviewItemName: '资格公平性检查', 
    "reviewItemCode": "qual_fairness",
    "reviewItemResult": 1,
    "errorNum": 1,
    "pointId": "1",
     "pointResult": 1,
     "resultId": "3",
      "reviewResult": 1,
      "fileText": "投标人应从其银行账户（基本存款账户）资质下列方式：公司公转账方式向招标文件载明的投标保证金账户交投标保证金，具体金额详见招标文件第一章。",
      "riskWarning": "投标人应从其银行账户（基本存款账户）资质下列方式：公司公转账方式向招标文件载明的投标保证金账户交投标保证金，具体金额详见招标文件第一章。的要求限定投标人缴纳保证金的形式， 违反了《招标投标领域公平竞争审查规则》中的规定，建议删除。",
      "legalBasicChapter": "《招标投标领域公平竞争审查规则》第二章第十一条",// 依据章节
      "legalBasic": "'政策制定机关可以通过招标投标信用评价引导经营主体诚信经营活动，可以根据实际情况制定实施相应政策措施或制定经营主体应用信用评价结果，但应平等对待不同地区、所有制形式的经营主体，依法保障经营主体自主权，不得制定以下政策措施：（三）限定经营主体缴纳保证金的形式'\"",
      "editSuggestion": "\"公司团队规模不做人数限制\"",
      "page": 3,
                                "position": [
                                    {
                                        "page": 3,
                                        "x1": 327,
                                        "y1": 616,
                                        "x2": 757,
                                        "y2": 736
                                    }
                                ]
  },
  {
    "id": "4",
    "label": "技术规格要求",
    "reviewItemName": "技术规格要求",
    "pointNum": 3,
    "children": [
      {
        "id": "4-1",
        "label": "不得将特定品牌、型号作为技术参数要求",
        "pointId": "point-4-1",
        "reviewResult": 1,
        "pointResult": 1,
        "resultId": "result-4-1",
        "page": 15,
        "position": [
          {
            "page": 15,
            "x1": 120,
            "y1": 250,
            "x2": 500,
            "y2": 320
          }
        ],
        "riskLevel": "risk",
        "description": "不得将特定品牌、型号作为技术参数要求",
        "riskDetails": "条款中指定了\"必须为XX品牌或同等产品\"，可能构成对特定供应商的倾向性。",
        "legalBasis": "《政府采购货物和服务招标投标管理办法》第十一条：采购人、采购代理机构不得以不合理的条件对供应商实行差别待遇或者歧视待遇。",
        "suggestion": "删除品牌限制，改为功能性技术参数描述",
        "likeCount": 2,
        "dislikeCount": 0
      },
      {
        "id": "4-2",
        "label": "技术参数设置合理性检查",
        "pointId": "point-4-2",
        "reviewResult": 0,
        "pointResult": 0,
        "resultId": "result-4-2",
        "page": 16,
        "position": [
          {
            "page": 16,
            "x1": 100,
            "y1": 180,
            "x2": 450,
            "y2": 230
          }
        ],
        "riskLevel": "safe",
        "description": "技术参数设置合理性检查",
        "legalBasis": "技术参数应当符合项目实际需要，具有合理性和必要性。",
        "likeCount": 1,
        "dislikeCount": 0
      }
    ]
  },
  {
    "id": "5",
    "label": "商务条件审查",
    "reviewItemName": "商务条件审查",
    "pointNum": 2,
    "children": [
      {
        "id": "5-1",
        "label": "付款条件合理性检查",
        "pointId": "point-5-1",
        "reviewResult": 0,
        "pointResult": 0,
        "resultId": "result-5-1",
        "page": 20,
        "position": [
          {
            "page": 20,
            "x1": 80,
            "y1": 300,
            "x2": 480,
            "y2": 350
          }
        ],
        "riskLevel": "safe",
        "description": "付款条件合理性检查",
        "legalBasis": "付款条件应当公平合理，不得设置过于苛刻的付款条件。",
        "likeCount": 0,
        "dislikeCount": 0
      },
      {
        "id": "5-2",
        "label": "履约保证金设置检查",
        "pointId": "point-5-2",
        "reviewResult": 1,
        "pointResult": 1,
        "resultId": "result-5-2",
        "page": 21,
        "position": [
          {
            "page": 21,
            "x1": 90,
            "y1": 200,
            "x2": 470,
            "y2": 250
          }
        ],
        "description": "履约保证金设置检查",
        "riskDetails": "履约保证金比例设置为15%，超过了法定上限10%的要求。",
        "legalBasis": "《政府采购法实施条例》第三十三条：履约保证金的数额不得超过政府采购合同金额的10%。",
        "suggestion": "将履约保证金比例调整为不超过10%",
        "likeCount": 3,
        "dislikeCount": 1
      }
    ]
  },
  {
    "id": "6",
    "label": "评标方法审查",
    "reviewItemName": "评标方法审查",
    "pointNum": 2,
    "children": [
      {
        "id": "6-1",
        "label": "评分标准设置合理性",
        "pointId": "point-6-1",
        "reviewResult": 0,
        "pointResult": 0,
        "resultId": "result-6-1",
        "page": 25,
        "position": [
          {
            "page": 25,
            "x1": 100,
            "y1": 150,
            "x2": 500,
            "y2": 200
          }
        ],
        "description": "评分标准设置合理性检查",
        "legalBasis": "评分标准应当量化、细化，具有可操作性。",
        "likeCount": 1,
        "dislikeCount": 0
      },
      {
        "id": "6-2",
        "label": "价格分权重设置检查",
        "pointId": "point-6-2",
        "reviewResult": 1,
        "pointResult": 1,
        "resultId": "result-6-2",
        "page": 26,
        "position": [
          {
            "page": 26,
            "x1": 120,
            "y1": 280,
            "x2": 520,
            "y2": 330
          }
        ],
        "description": "价格分权重设置检查",
        "riskDetails": "价格分权重设置为20%，低于法定最低要求30%。",
        "legalBasis": "《政府采购货物和服务招标投标管理办法》第五十五条：价格分值占总分值的比重不得低于30%。",
        "suggestion": "将价格分权重调整为不低于30%",
        "likeCount": 4,
        "dislikeCount": 0
      }
    ]
  },
  {
    "id": "7",
    "label": "其他合规性检查",
    "reviewItemName": "其他合规性检查",
    "pointNum": 1,
    "children": [
      {
        "id": "7-1",
        "label": "环保要求符合性检查",
        "pointId": "point-7-1",
        "reviewResult": 2,
        "pointResult": 2,
        "resultId": "result-7-1",
        "page": 35,
        "position": [
          {
            "page": 35,
            "x1": 100,
            "y1": 100,
            "x2": 400,
            "y2": 150
          }
        ],
        "description": "环保要求符合性检查",
        "legalBasis": "该项目不涉及环保要求，标记为不适用。",
        "likeCount": 0,
        "dislikeCount": 0
      }
    ]
  }
]

// 模拟审查清单数据
export const mockCheckList: CheckList = {
  id: 'checklist-1',
  name: '政府采购合规性审查清单',
  version: 'v2.1',
  categories: ['资质审查', '技术评审', '商务评审', '合同审查', '其他事项'],
  items: [
    {
      id: 'item-1',
      category: '资质审查',
      title: '营业执照有效性',
      description: '检查供应商营业执照是否在有效期内',
      required: true,
      completed: false,
      notes: '营业执照已过期，需要更新'
    },
    {
      id: 'item-2',
      category: '资质审查',
      title: '相关资质证书',
      description: '检查是否具备相关行业资质证书',
      required: true,
      completed: true,
    },
    {
      id: 'item-3',
      category: '技术评审',
      title: '技术参数符合性',
      description: '验证设备技术参数是否符合要求',
      required: true,
      completed: true,
    },
    {
      id: 'item-4',
      category: '技术评审',
      title: '技术方案可行性',
      description: '评估技术方案的可行性和先进性',
      required: true,
      completed: false
    },
    {
      id: 'item-5',
      category: '商务评审',
      title: '价格合理性',
      description: '分析投标价格的合理性',
      required: true,
      completed: true,
      riskLevel: 'safe'
    },
    {
      id: 'item-6',
      category: '商务评审',
      title: '付款条件',
      description: '检查付款条件是否合理',
      required: false,
      completed: false
    },
    {
      id: 'item-7',
      category: '合同审查',
      title: '合同条款完整性',
      description: '检查合同条款是否完整',
      required: true,
      completed: false,
      riskLevel: 'risk',
      notes: '部分条款表述不清'
    },
    {
      id: 'item-8',
      category: '合同审查',
      title: '违约责任条款',
      description: '检查违约责任条款是否明确',
      required: true,
      completed: false
    },
    {
      id: 'item-9',
      category: '其他事项',
      title: '环保要求',
      description: '检查是否符合环保要求',
      required: false,
      completed: true,
      riskLevel: 'na',
      notes: '该项目不涉及环保要求'
    },
    {
      id: 'item-10',
      category: '其他事项',
      title: '安全要求',
      description: '检查是否符合安全要求',
      required: false,
      completed: false
    }
  ],
  createdAt: '2024-01-15 08:00:00',
  updatedAt: '2024-01-15 10:45:00'
}

// 模拟PDF标注数据
export const mockPdfAnnotations = [
  {
    id: 'annotation-1',
    itemId: '1',
    pageNumber: 3,
    coordinates: {
      x: 100,
      y: 200,
      width: 300,
      height: 50
    },
    annotationType: 'risk',
    content: '供应商资质存在问题',
    createdAt: '2024-01-15 09:30:00'
  },
  {
    id: 'annotation-2',
    itemId: '2',
    pageNumber: 8,
    coordinates: {
      x: 150,
      y: 300,
      width: 400,
      height: 80
    },
    annotationType: 'safe',
    content: '技术规格符合要求',
    createdAt: '2024-01-15 09:45:00'
  }
]

// 导出所有模拟数据
export const mockData = {
  reviewTask: mockReviewTask,
  reviewItems: mockReviewItems,
  checkList: mockCheckList,
  pdfAnnotations: mockPdfAnnotations
}
